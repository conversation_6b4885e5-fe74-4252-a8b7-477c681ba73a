/**
 * Catalog interfaces and related types for the organization item catalog system
 * These interfaces match the backend DTOs exactly for proper API integration
 */

/**
 * Represents a product category in the system - matches ProductCategoryResponseDTO
 */
export interface Category {
  id?: number; // Optional for create requests, required for responses
  name: string;
  description: string;
  organizationId: number;
  imagePath?: string | null;
  imageUrl?: string | null; // Presigned URL for direct image viewing
}

/**
 * Represents a product subcategory in the system - matches ProductSubCategoryDTO
 */
export interface Subcategory {
  id?: number; // Optional for create requests, required for responses
  name: string;
  description: string;
  categoryId: number;
  organizationId: number;
  imagePath?: string | null;
  imageUrl?: string | null; // Presigned URL for direct image viewing

  // For UI display (not from backend DTO)
  categoryName?: string;
}

/**
 * Represents a product in the system - matches ProductDTO
 */
export interface Product {
  id?: number; // Optional for create requests, required for responses
  name: string;
  description: string;
  price: number;
  categoryId: number;
  subCategoryId: number;
  organizationId?: number;
  imagePaths?: string[] | null;

  // For UI display (not from backend DTO)
  subcategoryName?: string;
  categoryName?: string;
}

/**
 * Request object for creating a new category - matches ProductCategoryDTO structure
 */
export interface CreateCategoryRequest {
  name: string;
  description: string;
  organizationId: number;
  imageFile?: File | null;
}

/**
 * Request object for updating an existing category - matches ProductCategoryDTO structure
 */
export interface UpdateCategoryRequest {
  name: string;
  description: string;
  organizationId: number;
  imageFile?: File | null;
}

/**
 * Request object for creating a new subcategory - matches ProductSubCategoryDTO structure
 */
export interface CreateSubcategoryRequest {
  name: string;
  description: string;
  categoryId: number;
  organizationId: number;
  imagePath?: string | null;
}

/**
 * Request object for updating an existing subcategory - matches ProductSubCategoryDTO structure
 */
export interface UpdateSubcategoryRequest {
  name: string;
  description: string;
  categoryId: number;
  organizationId: number;
  imagePath?: string | null;
}

/**
 * Request object for creating a new product - matches ProductDTO structure
 */
export interface CreateProductRequest {
  name: string;
  description: string;
  price: number;
  categoryId: number;
  subCategoryId: number;
  organizationId?: number;
  imagePaths?: string[] | null;
}

/**
 * Request object for updating an existing product - matches ProductDTO structure
 */
export interface UpdateProductRequest {
  name: string;
  description: string;
  price: number;
  categoryId: number;
  subCategoryId: number;
  organizationId?: number;
  imagePaths?: string[] | null;
}
